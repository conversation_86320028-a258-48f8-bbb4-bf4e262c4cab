import React from 'react'
import { <PERSON><PERSON>, ToastProvider, useToast } from '@design-systems/apollo-ui'

// Example component showing how to add toasts for different positions
function ToastPositionExample() {
  const { add } = useToast()

  // Function to add a toast for a specific position
  const addToastForPosition = (position: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right') => {
    add({
      title: `${position.charAt(0).toUpperCase() + position.slice(1)} Toast`,
      description: `This toast appears at the ${position} position`,
      type: 'success',
      position: position,
      timeout: 3000, // Auto-dismiss after 3 seconds
    })
  }

  return (
    <div className="flex flex-col gap-4 p-4">
      <h2 className="text-xl font-bold">Toast Position Examples</h2>
      
      <div className="grid grid-cols-3 gap-2">
        {/* Top Row */}
        <Button 
          onClick={() => addToastForPosition('top-left')}
          variant="outline"
        >
          Top Left
        </Button>
        <Button 
          onClick={() => addToastForPosition('top-center')}
          variant="outline"
        >
          Top Center
        </Button>
        <Button 
          onClick={() => addToastForPosition('top-right')}
          variant="outline"
        >
          Top Right
        </Button>

        {/* Bottom Row */}
        <Button 
          onClick={() => addToastForPosition('bottom-left')}
          variant="outline"
        >
          Bottom Left
        </Button>
        <Button 
          onClick={() => addToastForPosition('bottom-center')}
          variant="outline"
        >
          Bottom Center
        </Button>
        <Button 
          onClick={() => addToastForPosition('bottom-right')}
          variant="outline"
        >
          Bottom Right
        </Button>
      </div>

      {/* Example with different toast types */}
      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">Different Toast Types</h3>
        <div className="flex gap-2">
          <Button 
            onClick={() => add({
              title: 'Success Toast',
              description: 'Operation completed successfully',
              type: 'success',
              position: 'top-right'
            })}
            variant="outline"
          >
            Success
          </Button>
          <Button 
            onClick={() => add({
              title: 'Error Toast',
              description: 'Something went wrong',
              type: 'error',
              position: 'top-right'
            })}
            variant="outline"
          >
            Error
          </Button>
          <Button 
            onClick={() => add({
              title: 'Warning Toast',
              description: 'Please review your input',
              type: 'warning',
              position: 'top-right'
            })}
            variant="outline"
          >
            Warning
          </Button>
          <Button 
            onClick={() => add({
              title: 'Info Toast',
              description: 'Here is some information',
              type: 'info',
              position: 'top-right'
            })}
            variant="outline"
          >
            Info
          </Button>
        </div>
      </div>
    </div>
  )
}

// Main App component with ToastProvider
export default function App() {
  return (
    <ToastProvider>
      <ToastPositionExample />
    </ToastProvider>
  )
}
