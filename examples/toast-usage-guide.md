# Toast Usage Guide

## Overview
The Apollo UI toast system provides a way to display temporary notifications to users. After creating the `toastManager={BaseToast.createToastManager()}` and `BaseToast.Provider`, you can easily add toasts for different positions.

## Basic Setup

1. **Wrap your app with ToastProvider:**
```tsx
import { ToastProvider } from '@design-systems/apollo-ui'

function App() {
  return (
    <ToastProvider>
      {/* Your app content */}
    </ToastProvider>
  )
}
```

2. **Use the useToast hook in your components:**
```tsx
import { useToast } from '@design-systems/apollo-ui'

function MyComponent() {
  const { add } = useToast()
  
  // Your component logic
}
```

## Adding Toasts for Different Positions

### Basic Toast
```tsx
const { add } = useToast()

add({
  title: "Success!",
  description: "Your action was completed successfully.",
  type: "success",
  position: "top-right" // Optional, defaults to "top-right"
})
```

### Available Positions
- `top-left`
- `top-center` 
- `top-right` (default)
- `bottom-left`
- `bottom-center`
- `bottom-right`

### Toast Types
- `success` - Green toast for successful operations
- `error` - Red toast for errors
- `warning` - Yellow toast for warnings
- `info` - Blue toast for information (default)

### Advanced Options
```tsx
add({
  id: "unique-toast-id", // Optional custom ID
  title: "Custom Toast",
  description: "This is a custom toast with advanced options",
  type: "success",
  position: "bottom-center",
  timeout: 5000, // Auto-dismiss after 5 seconds (0 = no auto-dismiss)
  onClose: () => {
    console.log("Toast was closed")
  },
  startDecorator: <Icon />, // Optional icon or element
  endDecorator: <Button>Action</Button>, // Optional action button
  className: "custom-toast-class" // Optional custom CSS class
})
```

## Toast Management

### Update an existing toast
```tsx
const { add, update } = useToast()

// Add a toast with a specific ID
add({
  id: "progress-toast",
  title: "Processing...",
  type: "info"
})

// Later, update it
update("progress-toast", {
  title: "Complete!",
  type: "success"
})
```

### Close a toast programmatically
```tsx
const { close } = useToast()

close("toast-id")
```

### Get all current toasts
```tsx
const { toasts } = useToast()

console.log("Current toasts:", toasts)
```

## Real-world Examples

### File Upload Progress
```tsx
const simulateFileUpload = () => {
  const toastId = `upload-${Date.now()}`
  
  add({
    id: toastId,
    title: "Uploading file...",
    description: "document.pdf",
    type: "info",
    timeout: 0 // Don't auto-dismiss
  })

  // Simulate upload completion
  setTimeout(() => {
    update(toastId, {
      title: "Upload complete",
      description: "document.pdf has been uploaded successfully.",
      type: "success",
      timeout: 3000 // Auto-dismiss after 3 seconds
    })
  }, 2000)
}
```

### Form Validation Error
```tsx
const handleFormError = () => {
  add({
    title: "Validation Error",
    description: "Please check the required fields and try again.",
    type: "error",
    position: "top-center"
  })
}
```

### Success Notification with Action
```tsx
const handleSaveSuccess = () => {
  add({
    title: "Document saved",
    description: "Your changes have been saved successfully.",
    type: "success",
    position: "bottom-right",
    endDecorator: (
      <Button variant="outline" size="small">
        View
      </Button>
    )
  })
}
```

## Notes

- The toast system automatically groups toasts by position
- Each position has its own toast manager instance
- Toasts are rendered in portals for proper z-index handling
- The system supports multiple toasts at the same position
- Toasts can be persistent (timeout: 0) or auto-dismissing
