import { useMemo } from "react"
import { Toast as BaseToast } from "@base-ui-components/react/toast"

import { ToastWrapper } from "../../components"
import {
  PositionProp,
  ToastObjectProps,
} from "../../components/toast/ToastProps"
import { useToast } from "../../hooks"
import { ToastProviderType } from "./ToastProviderType"

const ToastProviderContainer = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const { toasts, createToastManager } = useToast()
  const toastsByPosition = useMemo(() => {
    return toasts.reduce<Record<PositionProp, ToastObjectProps[]>>(
      (acc, toast) => {
        const pos = toast.position || "top-right"
        if (!acc[pos]) acc[pos] = []
        acc[pos].push(toast)
        return acc
      },
      {
        "top-left": [],
        "top-center": [],
        "top-right": [],
        "bottom-left": [],
        "bottom-center": [],
        "bottom-right": [],
      }
    )
  }, [toasts])

  return (
    <>
      {Object.entries(toastsByPosition).map(([toastPosition, toastList]) => {
        return (
          toastList &&
          toastList.length > 0 && (
            <BaseToast.Provider
              toastManager={BaseToast.createToastManager()}
              key={toastPosition}
              
            >
              <ToastWrapper
                key={`ApolloToast-wrapper-${toastPosition}`}
                position={toastPosition as PositionProp}
                // toasts={toastList}
              />
            </BaseToast.Provider>
          )
        )
      })}
      {children}
    </>
  )
}
export const ToastProvider = ({ children, ...props }: ToastProviderType) => {
  return (
    <BaseToast.Provider {...props}>
      <ToastProviderContainer>{children}</ToastProviderContainer>
    </BaseToast.Provider>
  )
}
